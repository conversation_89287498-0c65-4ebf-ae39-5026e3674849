#include "head.h"


int main(int argc, char const *argv[])
{
    printf("argc: %d\n", argc);

    for(int32_t i =0; i< argc; i++){
        printf("argv[%d]: %s\n", i, argv[i]);
        if(!strncmp(argv[i], "on", 2)){
            printf("on\n");
            const char* numStr = argv[i] + 3; // 指针移动到"on-"之后
            int32_t num = atoi(numStr); // 将字符串转换为int32_t
            printf("num: %d\n", num);

        } else if(!strncmp(argv[i], "off", 3)){
            printf("off\n");
            const char* numStr = argv[i] + 4; 
            int32_t num = atoi(numStr); // 将字符串转换为int32_t
            printf("num: %d\n", num);
        }
    }
    return 0;
}
