#include "head.h"


typedef struct _VCIFaultState {
    uint32_t faultType;         // 故障告警名称枚举值
    uint32_t faultState;      // 故障告警状态
    uint64_t faultRaiseTime;        // 故障告警发生时间
    uint64_t faultDownTime;         // 故障告警恢复时间
    uint32_t ShutDownType;            // 是否停机标志位
    uint32_t ShowType;                // 是否显示标志位
} VCIFaultState;

typedef struct _VCIContactorFaultSend {
    uint32_t gunNum;                                                  //  真实枪号
    uint32_t terminalID;									      //	终端ID
	uint32_t ContactorID;									//	主接触器编号
	std::vector<VCIFaultState> VCIFault;		//	VCI返回故障列表
}VCIContactorFaultSendDef;

typedef struct _vci_51_fsm_{
    uint32_t ContactorID;									//	主接触器编号
    std::vector<VCIContactorFaultSendDef> VCIFaultList;		//	VCI故障列表
}_vci_51_fsm;

std::vector<VCIFaultState> FaultList_;


void FaultMsgUpdate()
{
    _vci_51_fsm m_vci_51_fsm;
    _vci_51_fsm get_vci_51_fsm;
    uint32_t gun = 0;

    for(; gun < 2; gun++) {

    }
}


int main(int argc, char const *argv[])
{

    return 0;
}
